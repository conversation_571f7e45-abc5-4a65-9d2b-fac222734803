import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import {FlowDefinition} from "../../../../models/FlowDefinition";
import {ErrorPopupComponent} from "../../../error-popup/error-popup.component";
import {environment} from "../../../../../environments/environment";
import {finalize} from "rxjs/operators";
import {ServerService} from "../../../../services/server.service";
import {TypedJSON, jsonArrayMember } from "typedjson";
import {ChatDefinition} from "../../../../models/ChatDefinition";
import {BlockGroupModel} from "../../../../models/BlockGroupModel";
import {Integration} from "../../../../models/integration/Integration";
import {VariableDefinition} from "../../../../models/VariableDefinition";

@Component({
  selector: 'app-flow-variables-selector-dialog',
  templateUrl: './flow-variables-selector-dialog.component.html',
  styleUrls: ['./flow-variables-selector-dialog.component.scss']
})
export class FlowVariablesSelectorDialogComponent implements OnInit {
  loading: boolean = true;
  flowTitleParam: any = null;
  publishedFlows : FlowDefinition[];
  stagingFlows : FlowDefinition[];

  FlowToIgnore : number = null;
  SelectedFlow: FlowDefinition = null;
  Definition: ChatDefinition = null;
  Variables: VariableDefinition[] = null;

  @Output() AcceptAction: EventEmitter<VariableDefinition[]> = new EventEmitter<VariableDefinition[]>();

  constructor(private modalService: ModalService, private editorService : EditorService, private serverService: ServerService) {
  }

  ngOnInit() {
    this.loading = true;
    this.serverService.getFlows()
      .pipe(finalize( ()=> {this.loading = false;}))
      .subscribe( (flows : FlowDefinition[]) => {
          this.publishedFlows = flows.filter(f => f.ActiveProductionVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id));
          this.stagingFlows = flows.filter(f => f.ActiveStagingVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id));
        },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  closePopup() {
    this.modalService.removeLastElement();
  }

  onFlowSelected(flow: FlowDefinition, isStaging: boolean) {
    this.flowTitleParam = { flow: flow.name };
    this.SelectedFlow = flow;
    let blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;
    if (typeof(blob) === 'undefined') {
      this.loading = true;
      this.serverService.getFlow(flow.id)
        .pipe(finalize( ()=> {this.loading = false;}))
        .subscribe((downloadedFlow: FlowDefinition) => {
          flow.ActiveProductionVersion = downloadedFlow.ActiveProductionVersion;
          flow.ActiveStagingVersion = downloadedFlow.ActiveStagingVersion;
          blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;

          this.Definition = TypedJSON.parse(blob, ChatDefinition);
          this.Variables = this.Definition.VariableList;
        });
    }
    else {
      this.Definition = TypedJSON.parse(blob, ChatDefinition);
      this.Variables = this.Definition.VariableList;
    }
  }

  acceptPopup() {
    if (this.Variables === null) {
      return;
    }

    this.AcceptAction.emit(this.Variables);
    this.modalService.destroy();
  }

  goBack() {
    this.SelectedFlow = null;
    this.Variables = null;
  }

  getVariableType(variable: VariableDefinition) : string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }
}
