<div class="new-flow-container">
  <div class="title">{{ 'NEW_FLOW_TITLE' | translate }}</div>
  <div class="contents">
    <div class="name">
      <label>{{ 'FLOW_NAME' | translate }}:</label>
      <input #flownameinput maxlength="30" type="text" placeholder="{{'FLOW_NAME' | translate}}" class="input"
        [(ngModel)]="flowName" autofocus />
    </div>
    <div class="select-channel" *ngIf="showChannelType">
      <label>{{'NEW_FLOW_CHANNEL' | translate}}:</label>
      <select class="select" [(ngModel)]="selectedChannel">
        <option [ngValue]="channelType.Chat"><i class="fas fa-lg fa-comments"></i> Chat</option>
        <option [ngValue]="channelType.FacebookMessenger"><i class="fab fa-lg fa-facebook-messenger"></i> Facebook
          Messenger</option>
        <option [ngValue]="channelType.Instagram"><i class="fab fa-lg fa-instagram"></i> Instagram</option>
        <option [ngValue]="channelType.Twitter"><i class="fab fa-lg fa-twitter"></i> Twitter</option>
        <option [ngValue]="channelType.WhatsApp"><i class="fab fa-lg fa-whatsapp-square"></i> Whatsapp</option>
        <option [ngValue]="channelType.Skype"><i class="fab fa-lg fa-skype"></i> Skype</option>
        <option [ngValue]="channelType.Telegram"><i class="fab fa-lg fa-telegram"></i> Telegram</option>
        <option [ngValue]="channelType.Generic"><i class="fab fa-lg fa-generic"></i> Generic</option>
        <option [ngValue]="channelType.GoogleRBM"><i class="fab fa-lg fa-android"></i> Google RBM</option>
        <option [ngValue]="channelType.AppleMessaging"><i class="fab fa-lg fa-apple"></i> Apple Messages for Business
        </option>
      </select>
    </div>
    <div class="select-type" *ngIf="this.flowsTypes.length === 2 && ShowFlowType">
      <label>{{'NEW_FLOW_LITE' | translate}}:</label>
      <select class="select" [(ngModel)]="selectedFlowType">
        <option [ngValue]="flowType.Bot"><i class="fas fa-lg fa-comments"></i> Bot</option>
        <option [ngValue]="flowType.Lite"><i class="fas fa-lg fa-comments"></i> Lite</option>
      </select>
    </div>
    <div class="separator"></div>
    <div class="button-area">
      <button type="button" class="action-button" (click)="onCancel()">{{'CANCEL' | translate}}</button>
      <button type="button" class="action-button action-button-default" [disabled]="isInvalid()"
        (click)="onCreate()">{{'CREATE' | translate}}</button>
    </div>
  </div>
</div>