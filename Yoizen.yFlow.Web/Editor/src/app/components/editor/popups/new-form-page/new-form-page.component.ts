import {Component, EventEmitter, OnInit, Output} from '@angular/core';
import {ModalService} from '../../../../services/Tools/ModalService';
import {FormPageTypes} from "../../../../models/pieces/FormPiece";

@Component({
  selector: 'app-new-form-page',
  templateUrl: './new-form-page.component.html',
  styleUrls: ['./new-form-page.component.scss']
})
export class NewFormPageComponent implements OnInit {
  selectedType : FormPageTypes = FormPageTypes.select;
  formPageTypes = FormPageTypes;
  @Output() CreateAction = new EventEmitter<FormPageTypes>();

  constructor(
    private modalService: ModalService) {

  }

  onCreate() {
    this.CreateAction.emit(this.selectedType);
    this.modalService.destroy();
  }

  ngOnInit() {
  }

  onCancel() {
    this.modalService.destroy();
  }
}
