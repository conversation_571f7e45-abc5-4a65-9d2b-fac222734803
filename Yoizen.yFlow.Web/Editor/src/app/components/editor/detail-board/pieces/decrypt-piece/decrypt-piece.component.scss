@import "_variables";
@import "_mixins";

.decryptPiece {
  background-color: #fff;
  min-width: 500px;
  position: relative;
  
  textarea{
		border-radius: 10px;
		width: 100%;
		margin-bottom: 10px;
		font-weight: normal;
    min-width: 150px;
    min-height: 150px;
	}
  .card-title {
    margin-bottom: 10px;
  }

  .definition {
    padding: 10px;

    .option {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      .input {
        margin: 0 4px;
        width: 100%;
      }

    }
  }
  
}
