const base = require('./dailyBase');
const sequelize = require('../../helpers/sequelize');
const Flow = require('../flow');
const SocialServiceTypes = require('../../helpers/social-service-types');
const moment = require('moment');
const sumJson = require('../../helpers/Utils').sonJsonToDaily();
const { Sequelize, Op, TableHints } = require('sequelize');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const { localeServices } = require('../../i18n.config');
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");

class DailyByFlow extends base.DailyBase {
}
/**
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByFlow[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByFlow.getGroupDataByFlow = async function (start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        }
    };
    const group = [
        ['flow.id'],
        ['flow.name'],
        ['flow.channel']
    ];
    const attributes = [
        ...sumJson,
    ];
    return DailyByFlow.findAll({
        attributes: attributes,
        where: where,
        group: group,
        include: [{
            model: Flow,
            attributes: ['id', 'name', 'channel']
        }],
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        result = result.map((val) => {
            //val.data = JSON.parse(val.data);
            return val;
        });

        result = result.filter(item => {
            if (item.flow !== null) {
                return item;
            }
        })

        return result;
    }).catch((err) => {
        console.error(`error en getGroupDataByFlow, ${err}`);
        throw err;
    });
}

DailyByFlow.getAllByFlow = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        }
    };

    if (flowId !== 0) {
        where.flow_id = flowId;
    }

    var daily = await DailyByFlow.reduceAll({}, {
        include: [{
            model: Flow,
            attributes: ['id', 'name']
        }],
        where: where,
        order: [['id', 'DESC']]
    });

    daily = daily.map((val) => {
        val.channel = Object.keys(SocialServiceTypes).find(k => SocialServiceTypes[k] == val.channel);
        return val;
    });

    daily = daily.filter(item => {
        if (item.flow !== null) {
            return item;
        }
    })

    return daily;
}

DailyByFlow.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje) {
    localeServices.setLocale(lang);
    let daily = await DailyByFlow.getAllByFlow(flowId, start, end);

    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("NEW_CASES")
            , localeServices.translate("TRANSFERED_CASES")
            , localeServices.translate("CLOSED_CASES")
            , localeServices.translate("NEW_MESSAGES")
            , localeServices.translate("HSM_CASES")
            , localeServices.translate("ABANDONED_CASES")
            , localeServices.translate("MONTHLY_USERS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        daily.forEach((item, index) => {
            let date = moment(item.date).utc().add(offset, 'minute');
            let array = [
                date.format(),
                intervalTime(date).toString(),
                item.flow.name,
                item.channel,
                item.newCase,
                item.transferredToYSocial,
                item.closedByYFlow,
                item.newMessage,
                (item.hsmCase ? item.hsmCase : 0),
                (item.caseAbandoned ? item.caseAbandoned : 0),
                (item.monthlyUsers ? item.monthlyUsers : 0)
            ];

            totalWrites++;
            stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
        });

        if (totalWrites === totalCallbacks) {
            console.info(`Se terminó de escribir el archivo ${filename}`);

            stream.end();

            resolve(filename);

            return;
        }

        console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);
        let totalWaits = 0;

        let interval = setInterval(function () {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);

                stream.end();

                clearInterval(interval);

                resolve(filename);
            }
            else {
                totalWaits++;
                if (totalWaits === 5) {
                    console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);

                    stream.end();

                    clearInterval(interval);

                    resolve(filename);
                }
                else {
                    console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);
                }
            }
        }, 2000);
    });
}

DailyByFlow.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje) {
    let daily = await DailyByFlow.getAllByFlow(flowId, start, end);
    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    ws.cell(1, 1).string(localeServices.translate("DATE"));
    ws.cell(1, 2).string(localeServices.translate("INTERVAL"));
    ws.cell(1, 3).string(localeServices.translate("FLOW"));
    ws.cell(1, 4).string(localeServices.translate("CHANNEL"));
    ws.cell(1, 5).string(localeServices.translate("NEW_CASES"));
    ws.cell(1, 6).string(localeServices.translate("TRANSFERED_CASES"));
    ws.cell(1, 7).string(localeServices.translate("CLOSED_CASES"));
    ws.cell(1, 8).string(localeServices.translate("NEW_MESSAGES"));
    ws.cell(1, 9).string(localeServices.translate("HSM_CASES"));
    ws.cell(1, 10).string(localeServices.translate("ABANDONED_CASES"));
    ws.cell(1, 11).string(localeServices.translate("MONTHLY_USERS"));

    daily.forEach((item, index) => {
        var date = moment(item.date).utc().add(offset, 'minute');
        ws.cell(index + 2, 1).date(date);
        ws.cell(index + 2, 2).string(intervalTime(date).toString());
        ws.cell(index + 2, 3).string(item.flow.name);
        ws.cell(index + 2, 4).string(item.channel);
        ws.cell(index + 2, 5).number(item.newCase);
        ws.cell(index + 2, 6).number(item.transferredToYSocial);
        ws.cell(index + 2, 7).number(item.closedByYFlow);
        ws.cell(index + 2, 8).number(item.newMessage);
        ws.cell(index + 2, 9).number(item.hsmCase ? item.hsmCase : 0);
        ws.cell(index + 2, 10).number(item.caseAbandoned ? item.caseAbandoned : 0);
        ws.cell(index + 2, 11).number(item.monthlyUsers ? item.monthlyUsers : 0);
    });
    return wb;
}

DailyByFlow.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    newCase: {
        type: Sequelize.INTEGER,
        field: 'new_cases'
    },
    transferredToYSocial: {
        type: Sequelize.INTEGER,
        field: 'transferred'
    },
    closedByYFlow: {
        type: Sequelize.INTEGER,
        field: 'closed_by_yflow'
    },
    newMessage: {
        type: Sequelize.INTEGER,
        field: 'new_messages'
    },
    hsmCase: {
        type: Sequelize.INTEGER,
        field: 'hsm_case'
    },
    caseAbandoned: {
        type: Sequelize.INTEGER,
        field: 'case_abandoned'
    },
    monthlyUsers: {
        type: Sequelize.INTEGER,
        field: 'monthly_users'
    },
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_flow',
    tableName: 'history_daily_by_flow',
    timestamps: false
})

DailyByFlow.belongsTo(Flow);
module.exports = DailyByFlow;
