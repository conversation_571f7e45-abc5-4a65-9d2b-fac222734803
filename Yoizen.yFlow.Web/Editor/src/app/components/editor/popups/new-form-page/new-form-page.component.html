<div class="new-flow-container">
  <div class="title">{{ 'FORM_PAGES_ADD_TITLE' | translate }}</div>
  <div class="contents">
    <div class="select-channel">
      <label>{{'FORM_PAGE_TYPE' | translate}}:</label>
      <select class="select" [(ngModel)]="selectedType">
        <option [ngValue]="formPageTypes.select">{{'FORM_PAGE_TYPE_SELECT' | translate}}</option>
        <option [ngValue]="formPageTypes.datePicker">{{'FORM_PAGE_TYPE_DATEPICKER' | translate}}</option>
        <option [ngValue]="formPageTypes.input">{{'FORM_PAGE_TYPE_INPUT' | translate}}</option>
        <option [ngValue]="formPageTypes.picker">{{'FORM_PAGE_TYPE_PICKER' | translate}}</option>
      </select>
    </div>
    <div class="separator"></div>
    <div class="button-area">
      <button type="button" class="action-button" (click)="onCancel()">{{'CANCEL' | translate}}</button>
      <button type="button" class="action-button action-button-default"
              (click)="onCreate()">{{'CREATE' | translate}}</button>
    </div>
  </div>
</div>
