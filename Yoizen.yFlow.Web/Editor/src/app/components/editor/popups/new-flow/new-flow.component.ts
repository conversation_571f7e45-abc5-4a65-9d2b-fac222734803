import {Component, OnInit, EventEmitter, Input, ViewChild, ElementRef, Output, AfterViewInit} from '@angular/core';
import { Router } from '@angular/router';
import { ModalService } from '../../../../services/Tools/ModalService';
import { EditorService } from '../../../../services/editor.service';
import { ChannelTypes } from '../../../../models/ChannelType';
import { FlowTypes } from '../../../../models/FlowType';
import { ServerService } from '../../../../services/server.service';
import { geflowsTypes } from '../../../../Utils/window';

@Component({
  selector: 'app-new-flow',
  templateUrl: './new-flow.component.html',
  styleUrls: ['./new-flow.component.scss']
})
export class NewFlowComponent implements OnInit, AfterViewInit {
  flowName : string;
  channelType : any;
  selectedChannel : string;
  flowType : any;
  selectedFlowType : string;
  flowsTypes : string[] = [];
  showChannelType: boolean = true;

  @ViewChild('flownameinput', { static: false }) flownameInput : ElementRef;
  @Output() CreateAction = new EventEmitter<any>();
  @Input() ShowFlowType: boolean = true;
  @Input() DefaultChannel: ChannelTypes = null;

  constructor(
    private modalService: ModalService,
    private editorService : EditorService,
    private routerService : Router,
    private serverService : ServerService) {
  }

  ngOnInit() {
    this.channelType = ChannelTypes;
    this.flowType = FlowTypes;
    this.selectedChannel = ChannelTypes.FacebookMessenger;
    this.selectedFlowType = FlowTypes.Bot;

    if (typeof(geflowsTypes()) !== 'undefined') {
      geflowsTypes().split(',').forEach(item => {
        this.flowsTypes.push(item);
      });
    }

    if (this.DefaultChannel !== null){
      if (this.DefaultChannel !== ChannelTypes.Generic) {
        this.showChannelType = false;
      }
      this.selectedChannel = this.DefaultChannel;
    }

  }

  ngAfterViewInit() {
    if (this.flownameInput) {
      this.flownameInput.nativeElement.focus();
    }
  }

  onCreate() {
    if (this.flowsTypes.length === 1){
      this.selectedFlowType = this.flowsTypes[0].toLocaleLowerCase() === 'bot' ? '1' : '2';
    }
    this.CreateAction.emit({
      name: this.flowName, channel: this.selectedChannel, flowType: this.selectedFlowType
    });
  }

  isInvalid() : Boolean {
    return (this.flowName == null || this.flowName.length == 0);
  }

  onCancel() {
    this.modalService.destroy();
  }
}
