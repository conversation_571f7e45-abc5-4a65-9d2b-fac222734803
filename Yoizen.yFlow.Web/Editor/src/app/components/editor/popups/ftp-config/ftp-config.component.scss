
@import '_variables';
@import "_mixins";

.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;

  .title {
    font-size: 1.25rem;
    color: #2c3e50;
    margin: 0;
  }
}

.popup-content {
  padding: 1.5rem;
  overflow-y: auto;

  .section {
    margin-bottom: 2rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .field-group {
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      display: block;
      color: #34495e;
      font-weight: 500;
    }

    ui-switch {
      align-self: flex-start;
    }
  }

  .form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #dde1e7;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:focus {
      border-color: #e74c3c;
      box-shadow: 0 0 0 2px rgba(69, 193, 149, 0.1);
      outline: none;
    }

    &.operator {
      max-width: 100px;
    }
  }

  .alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.alert-danger {
      background: #fde8e8;
      color: #e74c3c;
    }
  }
}

.popup-footer {
  padding: 1.5rem;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &.btn-primary {
    background: #e74c3c;
    color: white;

    &:hover {
      background: darken(#e74c3c, 10%);
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: darken(#6c757d, 10%);
    }
  }
}

@media (max-width: 768px) {
  .popup-container {
    width: 95%;
  }
}
