import { ToasterService } from 'angular2-toaster';
import { ServerService } from 'src/app/services/server.service';
import { ModalService } from './../../../../services/Tools/ModalService';
import { TranslateService } from '@ngx-translate/core';
import { FTPData } from './../../../../models/FTPData';
import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';


@Component({
  selector: 'app-ftp-config',
  templateUrl: './ftp-config.component.html',
  styleUrls: ['./ftp-config.component.scss']
})
export class FtpConfigComponent implements OnInit {
  @Input() ftpData: FTPData = null;
  @Output() saveFtpAction = new EventEmitter<FTPData>();
  error: string;
  secure: boolean;
  implicit: boolean;

  constructor(private translate: TranslateService, private modalService: ModalService, private serverService: ServerService, private toasterService: ToasterService) {
  }

  ngOnInit() {
    this.error = undefined;
    if (!this.ftpData) {
      this.ftpData = new FTPData();
    }
    this.secure = (this.ftpData.secure !== 'false');
    this.implicit = (this.ftpData.secure === 'implicit');
  }

  onCancel() {
    this.modalService.destroy();
  }

  testConnection() {
    if (!this.ftpData.isValid()) {
      this.toasterService.pop('warning', this.translate.instant('FTP_INVALID_FIELDS'));
      return;
    }
    this.formatFtpData();
    this.serverService.testFtpConfig(this.ftpData).subscribe((result) => {
      this.toasterService.pop('success', this.translate.instant('CORRECT_FTP_CONFIG'));
    }, ({error}) => {
      this.toasterService.pop('error', this.translate.instant('INVALID_FTP_CONFIG'));
      console.log(error.message);
    });
  }

  formatFtpData() {
    if (this.secure) {
      if (this.implicit) {
        this.ftpData.secure = 'implicit';
      }
      else {
        this.ftpData.secure = 'true'
      }
    }
    else {
      this.ftpData.secure = 'false';
    }
  }

  saveFtp() {
    if (this.ftpData.isValid()) {
      this.formatFtpData();
      this.saveFtpAction.emit(this.ftpData);
      this.modalService.destroy();
    }
    else {
      this.error = this.translate.instant('FTP_INVALID_FIELDS');
    }
  }
}
