<div class="popup-overlay">
  <div class="popup-container">
    <div class="popup-header">
      <h3 class="title">{{'FTP_CONFIG' | translate}}</h3>
    </div>

    <div class="popup-content">
      <div class="alert alert-danger" *ngIf="error">
        <i class="fas fa-exclamation-triangle"></i>
        {{error}}
      </div>

      <div class="section">
        <div class="field-group">
          <label>{{ 'FTP_NAME' | translate }}</label>
          <input type="text" class="form-control" [(ngModel)]="ftpData.name">
        </div>

        <div class="field-group">
          <label>{{ 'FTP_SERVER' | translate }}</label>
          <input type="text" class="form-control" [(ngModel)]="ftpData.server">
        </div>

        <div class="field-group">
          <label>{{ 'FTP_PATH' | translate }}</label>
          <input type="text" class="form-control" [(ngModel)]="ftpData.path">
        </div>

        <div class="field-group">
          <label>{{ 'FTP_PORT' | translate }}</label>
          <input type="number" class="form-control" [(ngModel)]="ftpData.port" min="0">
        </div>

        <div class="field-group">
          <label>{{ 'FTP_SECURE' | translate }}</label>
          <ui-switch [(ngModel)]="secure" color="#45c195" size="small"></ui-switch>
        </div>

        <div class="field-group" *ngIf="secure">
          <label>{{ 'FTP_IMPLICIT' | translate }}</label>
          <ui-switch [(ngModel)]="implicit" color="#45c195" size="small"></ui-switch>
        </div>
      </div>

      <div class="section">
        <div class="field-group">
          <label>{{ 'FTP_ANONYMOUS' | translate }}</label>
          <ui-switch [(ngModel)]="ftpData.anonymous" color="#45c195" size="small"></ui-switch>
        </div>

        <div *ngIf="!ftpData.anonymous">
          <div class="field-group">
            <label>{{ 'FTP_USER' | translate }}</label>
            <input type="text" class="form-control" [(ngModel)]="ftpData.username">
          </div>

          <div class="field-group">
            <label>{{ 'FTP_PASSWORD' | translate }}</label>
            <input type="password" class="form-control" [(ngModel)]="ftpData.password">
          </div>
        </div>
      </div>

      <div class="section">
        <div class="field-group">
          <label>{{ 'FTP_FORMAT' | translate }}</label>
          <select class="form-control" [(ngModel)]="ftpData.saveInExcel">
            <option [ngValue]="true">{{ 'FTP_EXCEL' | translate}}</option>
            <option [ngValue]="false">{{ 'FTP_TEXT' | translate}}</option>
          </select>
        </div>

        <div class="field-group" *ngIf="!ftpData.saveInExcel">
          <label>{{ 'FTP_OPERATOR' | translate }}</label>
          <input type="text" class="form-control operator" [(ngModel)]="ftpData.operator">
        </div>
      </div>
    </div>

    <div class="popup-footer">
      <button class="btn btn-secondary" (click)="onCancel()">
        {{ 'CANCEL' | translate }}
      </button>
      <button class="btn btn-secondary" (click)="testConnection()">
        {{ 'TEST_FTP_CONNECTION' | translate }}
      </button>
      <button class="btn btn-primary" (click)="saveFtp()">
        {{ 'ACCEPT' | translate }}
      </button>
    </div>
  </div>
</div>
