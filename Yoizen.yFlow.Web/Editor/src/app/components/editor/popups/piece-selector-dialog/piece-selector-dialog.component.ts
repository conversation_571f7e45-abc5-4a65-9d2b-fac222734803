import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import {CommandDefinition} from "../../../../models/commands/CommandDefinition";
import {SectionType} from "../../../../models/MenuType";
import {Integration} from "../../../../models/integration/Integration";
import {PieceType} from "../../../../models/PieceType";

@Component({
  selector: 'app-piece-selector-dialog',
  templateUrl: './piece-selector-dialog.component.html',
  styleUrls: ['./piece-selector-dialog.component.scss']
})
export class PieceSelectorDialogComponent implements OnInit {
  @Output() PieceSelected : EventEmitter<PieceType> = new EventEmitter<PieceType>();

  constructor(private editorService : EditorService, private modalService: ModalService) {

  }

  ngOnInit() {
  }

  closePopup() {
    this.modalService.removeLastElement();
  }

  onPieceSelected(pieceType: PieceType) {
    this.closePopup();
    this.PieceSelected.emit(pieceType);
  }
}
