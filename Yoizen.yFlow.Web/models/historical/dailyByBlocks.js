const base = require('./dailyBase');
const { Sequelize, Op, TableHints } = require('sequelize');
const sequelize = require('../../helpers/sequelize');
const Flow = require('../flow');
const moment = require('moment');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const { localeServices } = require('../../i18n.config');
const groupYear = require('../../helpers/Utils').groupYear;
const groupMonth = require('../../helpers/Utils').groupMonth;
const groupDay = require('../../helpers/Utils').groupDay;
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");
const sql = require('mssql');

class DailyByBlocks extends base.DailyBase {
}


/**
 * @param {number} flowId flow a filtrar
 * @param {number[]} blocksId bloques a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @param {number} offset offset
 * @returns {Promise<DailyByBlocks[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByBlocks.getGroupByDay = async function (flowId, blocksId, start, end, offset) {
    var where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };

    if (blocksId !== undefined &&
        blocksId !== null &&
        blocksId.length !== 0) {
        where.block_id = {
            [Op.in]: blocksId
        };
    }

    const group = [
        groupYear(offset),
        groupMonth(offset),
        groupDay(offset),
        ['block_id']
    ]

    const order = [[sequelize.literal('date')], ['block_id', 'asc']];
    const atrributes = [
        [sequelize.fn('CONCAT', groupYear(offset), '-', groupMonth(offset), '-', groupDay(offset)), 'date'],
        'blockId',
        [sequelize.fn('sum', sequelize.col('total')), 'total']
    ];

    return DailyByBlocks.findAll({
        attributes: atrributes,
        where: where,
        group: group,
        order: order,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return DailyByBlocks.setNameToBlocks(flowId, result).then((blocks) => {
            return blocks;
        }).catch((err) => {
            console.error(`error en setNameToBlock, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByDay, ${err}`);
        throw err;
    });
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByBlocks[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByBlocks.getGroupByBlock = async function (flowId, start, end) {

    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };
    const group = ['flow_id', 'block_id'];
    const attributes = [
        'blockId',
        [sequelize.fn('sum',
            sequelize.col('total'),
        ), 'total'],
    ];

    return DailyByBlocks.findAll({
        attributes: attributes,
        where: where,
        group: group,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return DailyByBlocks.setNameToBlocks(flowId, result).then((blocks) => {
            return blocks;
        }).catch((err) => {
            console.error(`error en setNameToBlocks, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByBlock, ${err}`);
        throw err;
    });
}

DailyByBlocks.setNameToBlocks = async function (flowId, blocks) {
    if (blocks.length === 0) {
        return blocks;
    }
    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }
        let version = flow.ActiveProductionVersion;

        var result = Array();
        blocks.forEach((val) => {
            var block = version.blob.BlockList.find(aux => aux.Id === val.blockId);

            if (!block) {
                block = version.blob.DefaultBlocks.find(aux => aux.Id === val.blockId);
            }

            if (!block) {
                if (version.blob.BlockDeletedList) {
                    block = version.blob.BlockDeletedList.find(aux => aux.Id === val.blockId);
                    if (block) {
                        block.SystemProtected = false;
                        block.Deleted = true;
                    }
                }
            }

            if (block) {
                if (block.Deleted === undefined) {
                    block.Deleted = false;
                }

                block.Pieces = null;

                val.block = block
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToBlocks, ${err}`);
        throw err;
    });
}

DailyByBlocks.setNameToBlocks = async function (flowId, blocks) {
    if (blocks.length === 0) {
        return blocks;
    }

    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }

        var result = Array();
        blocks.forEach((val) => {
            const block = DailyByBlocks.setNameToBlock(flow.ActiveProductionVersion, val.blockId);

            if (block) {
                val.block = block
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToBlocks, ${err}`);
        throw err;
    });
}

DailyByBlocks.setNameToBlock = function (version, blockId) {
    var block = version.blob.BlockList.find(aux => aux.Id === blockId);

    if (!block) {
        block = version.blob.DefaultBlocks.find(aux => aux.Id === blockId);
    }

    if (!block) {
        if (version.blob.BlockDeletedList) {
            block = version.blob.BlockDeletedList.find(aux => aux.Id === blockId);
            if (block) {
                block.SystemProtected = false;
                block.Deleted = true;
            }
        }
    }

    if (block) {
        if (block.Deleted === undefined) {
            block.Deleted = false;
        }

        block.Pieces = null;
    }

    return block;
}

DailyByBlocks.getAllByFlow = async function (flowId, start, end, callbackRow) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flowId: flowId
    };

    let flow = await Flow.findLastProductionById(flowId);
    if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
        flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
    }
    let version = flow.ActiveProductionVersion;

    const query = sequelize.dialect.queryGenerator.selectQuery('history_daily_by_blocks', {
        where: where,
        tableHint: TableHints.NOLOCK
    }).slice(0, -1);
    
    let dbConfig = JSON.parse(JSON.stringify(sequelize.dbConfig));
    dbConfig.requestTimeout = 0;

    return new Promise(async function (resolve, reject) {
        try {
            await sql.connect(dbConfig);

            const request = new sql.Request();
            request.stream = true;

            request.on('row', row => {
                let val = new DailyByBlocks();
                val.date = moment(row.date);
                val.datetime = moment(row.interval_datetime);
                val.interval = row.interval;
                val.flowId = row.flow_id;
                val.blockId = row.block_id;
                val.channel = row.channel;
                val.version = row.version;
                val.total = row.total;
                val.flow = flow;

                let block = DailyByBlocks.setNameToBlock(version, val.blockId);
                val.block = block;

                if (typeof (callbackRow) === 'function') {
                    callbackRow(val);
                }
            });

            request.on('error', err => {
                console.error(`Ocurrió un error: ${JSON.stringify(err)}`);
                try {
                    sql.close(); 
                } catch (closeErr) {
                    console.error(`Error al cerrar la conexión: ${JSON.stringify(closeErr)}`);
                }
                reject(err);
            });

            request.on('done', result => {
                console.log(`Terminó la query: ${JSON.stringify(result)}`);
                sql.close();
                resolve();
            });

            console.log(`Se ejecutará la query: ${query}`);
            request.query(query);
        }
        catch (err) {
            console.error(`Error conectando a la base de datos: ${JSON.stringify(err)}`);
            try {
                sql.close();
            } catch (closeErr) {
                console.error(`Error al cerrar la conexión: ${JSON.stringify(closeErr)}`);
            }
            reject(err);
        }
    });
};

DailyByBlocks.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje) {
    localeServices.setLocale(lang);
    
    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("VERSION")
            , localeServices.translate("BLOCK")
            , localeServices.translate("DELETE")
            , localeServices.translate("SYSTEM")
            , localeServices.translate("EXECUTIONS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        let rowsCallback = 0;
        DailyByBlocks.getAllByFlow(flowId, start, end, function(item) {
            if (item && item.block) { 
                let date = moment(item.date).utc().add(offset, 'minute');
                let array = [
                    date.format(),
                    intervalTime(date).toString(),
                    item.flow.name,
                    item.channel,
                    item.version ? item.version.toString() : '', 
                    item.block.Name || '', 
                    (item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false'),
                    (item.block.SystemProtected !== undefined ? item.block.SystemProtected.toString() : 'false'),
                    item.total
                ];

                totalWrites++;
                stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
            }
            
            rowsCallback++;
            if (rowsCallback % 10000 === 0) {
                console.log(`Ya se procesaron ${rowsCallback} registros`);
            }
        }).then(() => {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);
                stream.end();
                resolve(filename);
                return;
            }


            console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);

            let totalWaits = 0;

            let interval = setInterval(function () {
                if (totalWrites === totalCallbacks) {
                    console.info(`Se terminó de escribir el archivo ${filename}`);
                    stream.end();
                    clearInterval(interval);
                    resolve(filename);
                }
                else {
                    totalWaits++;
                    if (totalWaits === 5) {
                        console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);
                        stream.end();
                        clearInterval(interval);
                        resolve(filename);
                    }
                    else {
                        console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);                   }
                }
            }, 2000);
        }).catch(error => {
            console.error(`Error al generar el reporte: ${error}`);
            stream.end();
            resolve(filename);
        });
    });
};

DailyByBlocks.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje) {
    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    let rowIndex = 1;
    let columnIndex = 1;
    
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("DATE"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("INTERVAL"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("FLOW"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("CHANNEL"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("VERSION"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("BLOCK"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("DELETE"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("SYSTEM"));
    ws.cell(rowIndex, columnIndex++).string(localeServices.translate("EXECUTIONS"));

    rowIndex++;
    
    let rowsCallback = 0;
    try {
        await DailyByBlocks.getAllByFlow(flowId, start, end, function(item) {
            if (item && item.block) { 
                columnIndex = 1;
                var date = moment(item.date).utc().add(offset, 'minute');
                ws.cell(rowIndex, columnIndex++).date(date);
                ws.cell(rowIndex, columnIndex++).string(intervalTime(date).toString());
                ws.cell(rowIndex, columnIndex++).string(item.flow ? item.flow.name : '');
                ws.cell(rowIndex, columnIndex++).string(item.channel || ''); 
                ws.cell(rowIndex, columnIndex++).string(item.version ? item.version.toString() : ''); 
                ws.cell(rowIndex, columnIndex++).string(item.block.Name || '');
                ws.cell(rowIndex, columnIndex++).string(item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false');
                ws.cell(rowIndex, columnIndex++).string(item.block.SystemProtected !== undefined ? item.block.SystemProtected.toString() : 'false');
                ws.cell(rowIndex, columnIndex++).number(item.total || 0); 
                rowIndex++;
            }
            
            rowsCallback++;
            if (rowsCallback % 10000 === 0) {
                console.log(`Ya se procesaron ${rowsCallback} registros`);
            }
        });
    } catch (error) {
        console.error(`Error al generar el Excel: ${error}`);
    }

    return wb;
};


DailyByBlocks.init({
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    total: Sequelize.INTEGER,
    version: Sequelize.INTEGER,
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    blockId: {
        type: Sequelize.STRING,
        field: 'block_id',
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_blocks',
    tableName: 'history_daily_by_blocks',
    timestamps: false
})

DailyByBlocks.belongsTo(Flow);
module.exports = DailyByBlocks;
