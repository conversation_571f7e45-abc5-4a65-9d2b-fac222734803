const base = require('./dailyBase');
const { Sequelize, Op, TableHints } = require('sequelize');
const sequelize = require('../../helpers/sequelize');
const Flow = require('../flow');
const moment = require('moment');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const { localeServices } = require('../../i18n.config');
const groupYear = require('../../helpers/Utils').groupYear;
const groupMonth = require('../../helpers/Utils').groupMonth;
const groupDay = require('../../helpers/Utils').groupDay;
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");

class DailyByCommands extends base.DailyBase {
}

DailyByCommands.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    total: Sequelize.INTEGER,
    version: Sequelize.INTEGER,
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    commandId: {
        type: Sequelize.INTEGER,
        field: 'command_id',
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_commands',
    tableName: 'history_daily_by_commands',
    timestamps: false
})

/**
 * @param {number} flowId flow a filtrar
 * @param {number[]} integrationsId integraciobes a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @param {number} offset offset
 * @returns {Promise<DailyByCommands[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByCommands.getGroupByDay = async function (flowId, commandsId, start, end, offset) {
    var where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };

    if (commandsId !== undefined &&
        commandsId !== null &&
        commandsId.length != 0) {

        where.command_id = {
            [Op.in]: commandsId
        };
    }

    const group = [
        groupYear(offset),
        groupMonth(offset),
        groupDay(offset),
        ['command_id']
    ]

    const order = [[sequelize.literal('date')], ['command_id', 'asc']];
    const atrributes = [
        [sequelize.fn('CONCAT', groupYear(offset), '-', groupMonth(offset), '-', groupDay(offset)), 'date'],
        'commandId',
        [sequelize.fn('sum', sequelize.col('total')), 'total']
    ];

    return DailyByCommands.findAll({
        attributes: atrributes,
        where: where,
        group: group,
        order: order,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToCommand(flowId, result).then((commands) => {
            return commands;
        }).catch((err) => {
            console.error(`error en setNameToCommand, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByDay, ${err}`);
        throw err;
    });
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByCommands[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByCommands.getGroupByCommand = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flowId: flowId
    };
    const group = ['flow_id', 'command_id'];
    const attributes = [
        'commandId',
        [sequelize.fn('sum',
            sequelize.col('total'),
        ), 'total'],
    ];

    return DailyByCommands.findAll({
        attributes: attributes,
        where: where,
        group: group,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToCommand(flowId, result).then((commands) => {
            return commands;
        }).catch((err) => {
            console.error(`error en setNameToCommand, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByCommand, ${err}`);
        throw err;
    });
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByCommands[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByCommands.getAllByFlow = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };

    return await DailyByCommands.reduceAll({}, {
        where: where,
        include: [{
            model: Flow,
            attributes: ['id', 'name']
        }],
        order: [['id', 'DESC']]
    }).then((result) => {
        return setNameToCommand(flowId, result).then((commands) => {
            return commands;
        }).catch((err) => {
            console.error(`error en setNameToCommand, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByIntegration, ${err}`);
        throw err;
    });
}

DailyByCommands.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje) {
    localeServices.setLocale(lang);
    let dailyByCommands = await DailyByCommands.getAllByFlow(flowId, start, end, offset);

    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("VERSION")
            , localeServices.translate("COMMAND")
            , localeServices.translate("DELETE")
            , localeServices.translate("EXECUTIONS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        dailyByCommands.forEach((item) => {
            if (item.command) {
                let date = moment(item.date).utc().add(offset, 'minute');
                let array = [
                    date.format(),
                    intervalTime(date).toString(),
                    item.flow.name,
                    item.channel,
                    item.version.toString(),
                    item.command.name,
                    (item.command.deleted !== undefined ? item.command.deleted.toString() : 'false'),
                    item.total
                ];

                totalWrites++;
                stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
            }
        });

        if (totalWrites === totalCallbacks) {
            console.info(`Se terminó de escribir el archivo ${filename}`);

            stream.end();

            resolve(filename);

            return;
        }

        console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);
        let totalWaits = 0;

        let interval = setInterval(function () {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);

                stream.end();

                clearInterval(interval);

                resolve(filename);
            }
            else {
                totalWaits++;
                if (totalWaits === 5) {
                    console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);

                    stream.end();

                    clearInterval(interval);

                    resolve(filename);
                }
                else {
                    console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);
                }
            }
        }, 2000);
    });
}

DailyByCommands.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje) {
    let dailyByCommands = await DailyByCommands.getAllByFlow(flowId, start, end, offset);
    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    ws.cell(1, 1).string(localeServices.translate("DATE"));
    ws.cell(1, 2).string(localeServices.translate("INTERVAL"));
    ws.cell(1, 3).string(localeServices.translate("FLOW"));
    ws.cell(1, 4).string(localeServices.translate("CHANNEL"));
    ws.cell(1, 5).string(localeServices.translate("VERSION"));
    ws.cell(1, 6).string(localeServices.translate("COMMAND"));
    ws.cell(1, 7).string(localeServices.translate("DELETE"));
    ws.cell(1, 8).string(localeServices.translate("EXECUTIONS"));

    var index = 2;
    dailyByCommands.forEach((item) => {
        if (item.command) {
            let date = moment(item.date).utc().add(offset, 'minute');
            ws.cell(index, 1).date(date);
            ws.cell(index, 2).string(intervalTime(date).toString());
            ws.cell(index, 3).string(item.flow.name);
            ws.cell(index, 4).string(item.channel);
            ws.cell(index, 5).string(item.version.toString());
            ws.cell(index, 6).string(item.command.name);
            ws.cell(index, 7).string(item.command.deleted !== undefined ? item.command.deleted.toString() : 'false');
            ws.cell(index, 8).number(item.total);
            index++;
        }
    });
    return wb;
}

const setNameToCommand = async function (flowId, commands) {
    if (commands.length === 0) {
        return commands;
    }

    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }
        let version = flow.ActiveProductionVersion;

        var result = Array();
        commands.forEach((val) => {
            var command = version.blob.CommandDefinitions.find(aux => aux.id === val.commandId);

            if (!command) {
                if (version.blob.CommandDeletedDefinitions) {
                    command = version.blob.CommandDeletedDefinitions.find(aux => aux.id === val.commandId);
                    if (command) {
                        command.deleted = true;
                    }
                }
            }

            if (command) {
                if (command.deleted === undefined) {
                    command.deleted = false;
                }

                val.command = command;
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToCommand, ${err}`);
        throw err;
    });
}

DailyByCommands.belongsTo(Flow);
module.exports = DailyByCommands;
