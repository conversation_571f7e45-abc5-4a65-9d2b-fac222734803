<div class="flow-selector-container" *ngIf="SelectedFlow === null">
  <div class="title">{{ 'FLOW_SELECTOR_TITLE' | translate }}</div>
  <div class="contents">
    <!--TODO: ver como meter esto de nuevo
    <h2>{{ 'FLOWS_PUBLISHED' | translate }}</h2>
    <app-dashboard-flow *ngFor="let flow of publishedFlows" class="content"
                        [model]="flow"
                        [isStaging]="false"
                        [showActions]="false"
                        [onSelect]="onFlowSelected.bind(this)"></app-dashboard-flow>
    <label *ngIf="!publishedFlows || publishedFlows.length == 0">{{'NO_FLOWS' | translate}}</label>-->
    <h2>{{ 'FLOWS_DEVELOPMENT' | translate }}</h2>
    <app-dashboard-flow *ngFor="let flow of stagingFlows" class="content development"
                        [model]="flow"
                        [showActions]="false"
                        [onSelect]="onFlowSelected.bind(this)"></app-dashboard-flow>
    <label *ngIf="!stagingFlows || stagingFlows.length == 0">{{'NO_FLOWS' | translate}}</label>
  </div>
  <div class="button-area">
    <div class="action-button action-button-default" (click)="closePopup()">{{ 'CANCEL' | translate}}</div>
  </div>
</div>

<div class="variables-selector-container" *ngIf="SelectedFlow !== null">
  <div class="title">{{ 'VARIABLES_SELECTOR_FROM_FLOW_TITLE' | translate:flowTitleParam }}</div>
  <div class="contents">
    <div class="empty" *ngIf="Variables !== null && Variables.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'VARIABLES_EMPTY' | translate }}
      </div>
    </div>
    <div class="variables" *ngIf="Variables !== null && Variables.length > 0">
      <h3>{{'VARIABLES_USER_LIST' | translate}}</h3>
      <div class="variables-table">
        <div class="variables-table-header">
          <div>{{'NAME' | translate}}</div>
          <div>{{'VARIABLE_DEFAULT_VALUE' | translate}}</div>
          <div>{{'VARIABLE_CONSTANT' | translate}}</div>
        </div>
        <div class="variables-table-row" *ngFor="let variable of Variables">
          <div class="variables-table-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
          <div class="variables-table-cell"><span class="variable-defaultvalue">{{ variable.DefaultValue | translate }}</span></div>
          <div class="variables-table-cell"><span class="variable-constant fa fa-check-circle" *ngIf="variable.Constant"></span></div>
        </div>
      </div>
    </div>
  </div>
  <div class="button-area">
    <div class="action-button" (click)="goBack()">{{ 'BACK' | translate}}</div>
    <div class="action-button action-button-default" (click)="acceptPopup()" [ngClass]="{'disabled': Variables === null || Variables.length === 0}">{{ 'ACCEPT' | translate}}</div>
    <div class="action-button" (click)="closePopup()">{{ 'CANCEL' | translate}}</div>
  </div>
</div>
