const base = require('./dailyBase');
const { Sequelize, Op, TableHints } = require('sequelize');
const sequelize = require('../../helpers/sequelize');
const Flow = require('../flow');
const moment = require('moment');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const DailyByBlocks = require('./dailyByBlocks');
const { localeServices } = require('../../i18n.config');
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}
class dailyByBlocksSequence extends base.DailyBase {
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByBlocks[]>} Devuelve una lista del tipo DailyByFlow
 */
dailyByBlocksSequence.getGroupByBlockSequence = async function (flowId, blocksId, start, end, leftLevel, rightLevel) {

    const tempSQL = sequelize.dialect.queryGenerator.selectQuery('history_daily_by_blocks_sequence', {
        attributes: ['source_block_id'],
        where: {
            date: {
                [Op.between]: [start.toDate(), end.toDate()]
            },
            flow_id: flowId,
            [Op.or]: [
                { source_block_id: blocksId },
                { dest_block_id: blocksId }
            ]
        },
        tableHint: TableHints.NOLOCK
    }).slice(0, -1);

    const tempSQL2 = sequelize.dialect.queryGenerator.selectQuery('history_daily_by_blocks_sequence', {
        attributes: ['dest_block_id'],
        where: {
            date: {
                [Op.between]: [start.toDate(), end.toDate()]
            },
            flow_id: flowId,
            [Op.or]: [
                { source_block_id: blocksId },
                { dest_block_id: blocksId }
            ]
        },
        tableHint: TableHints.NOLOCK
    }).slice(0, -1);


    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId,
        [Op.or]: [
            { source_block_id: blocksId },
            { dest_block_id: blocksId },
            /*{source_block_id: {
                [Op.in]: sequelize.literal(`(${tempSQL})`)
            }},*/
            rightLevel ? {
                source_block_id: {
                    [Op.in]: sequelize.literal(`(${tempSQL2})`)
                }
            } : {},
            leftLevel ? {
                dest_block_id: {
                    [Op.in]: sequelize.literal(`(${tempSQL})`)
                }
            } : {},
            /*{dest_block_id: {
                [Op.in]: sequelize.literal(`(${tempSQL2})`)
            }},*/
        ]
    };
    const group = ['flow_id', 'dest_block_id', 'source_block_id'];
    const attributes = [
        'destBlockId',
        'sourceBlockId',
        [sequelize.fn('sum',
            sequelize.col('total'),
        ), 'total'],
    ];

    return dailyByBlocksSequence.findAll({
        attributes: attributes,
        where: where,
        group: group,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToBlock(flowId, result).then((blocks) => {
            return blocks;
        }).catch((err) => {
            console.error(`error en setNameToBlock, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByBlockSequence, ${err}`);
        throw err;
    });
}

dailyByBlocksSequence.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje) {
    let dailyByBlocksSequences = await dailyByBlocksSequence.getAllByFlow(flowId, start, end);

    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    ws.cell(1, 1).string(localeServices.translate("DATE"));
    ws.cell(1, 2).string(localeServices.translate("INTERVAL"));
    ws.cell(1, 3).string(localeServices.translate("FLOW"));
    ws.cell(1, 4).string(localeServices.translate("CHANNEL"));
    ws.cell(1, 5).string(localeServices.translate("VERSION"));
    ws.cell(1, 6).string(localeServices.translate("SOURCE_BLOCK"));
    ws.cell(1, 7).string(localeServices.translate("DEST_BLOCK"));
    ws.cell(1, 8).string(localeServices.translate("EXECUTIONS"));

    var index = 2;
    dailyByBlocksSequences.forEach((item) => {
        if (item.sourceBlock && item.destBlock) {
            var date = moment(item.date).utc().add(offset, 'minute');
            ws.cell(index, 1).date(date);
            ws.cell(index, 2).string(intervalTime(date).toString());
            ws.cell(index, 3).string(item.flow.name);
            ws.cell(index, 4).string(item.channel);
            ws.cell(index, 5).string(item.version.toString());
            ws.cell(index, 6).string(item.sourceBlock.Name);
            ws.cell(index, 7).string(item.destBlock.Name);
            //ws.cell(index, 7).string(item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false');
            //ws.cell(index, 8).string(item.block.SystemProtected !== undefined ? item.block.SystemProtected.toString() : 'false');
            ws.cell(index, 8).number(item.total);
            index++;
        }
    });

    return wb;
}

dailyByBlocksSequence.getAllByFlow = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flowId: flowId
    };

    return dailyByBlocksSequence.reduceAll({}, {
        where: where,
        include: [{
            model: Flow,
            attributes: ['id', 'name']
        }],
    }).then((result) => {
        return setNameToBlock(flowId, result).then((commands) => {
            return commands;
        }).catch((err) => {
            console.error(`error en setNameToBlocks, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en setNameToBlocks, ${err}`);
        throw err;
    });
}

dailyByBlocksSequence.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje) {
    localeServices.setLocale(lang);
    let dailyByBlocksSequences = await dailyByBlocksSequence.getAllByFlow(flowId, start, end);

    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("VERSION")
            , localeServices.translate("SOURCE_BLOCK")
            , localeServices.translate("DEST_BLOCK")
            , localeServices.translate("EXECUTIONS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        dailyByBlocksSequences.forEach((item) => {
            if (item.sourceBlock && item.destBlock) {
                let date = moment(item.date).utc().add(offset, 'minute');
                let array = [
                    date.format(),
                    intervalTime(date).toString(),
                    item.flow.name,
                    item.channel,
                    item.version.toString(),
                    item.sourceBlock.Name,
                    item.destBlock.Name,
                    item.total
                ];

                totalWrites++;
                stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
            }
        });

        if (totalWrites === totalCallbacks) {
            console.info(`Se terminó de escribir el archivo ${filename}`);

            stream.end();

            resolve(filename);

            return;
        }

        console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);
        let totalWaits = 0;

        let interval = setInterval(function () {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);

                stream.end();

                clearInterval(interval);

                resolve(filename);
            }
            else {
                totalWaits++;
                if (totalWaits === 5) {
                    console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);

                    stream.end();

                    clearInterval(interval);

                    resolve(filename);
                }
                else {
                    console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);
                }
            }
        }, 2000);
    });
}

dailyByBlocksSequence.getBlocksUsageComparison = async function (flowId, start, end) {
    const flow = await Flow.findLastProductionById(flowId);

    if (!flow || !flow.ActiveProductionVersion || !flow.ActiveProductionVersion.blob) {
        return {
            executedBlocks: [],
            unusedBlocks: []
        };
    }

    if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
        flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
    }

    const allBlocks = [
        ...flow.ActiveProductionVersion.blob.BlockList,
        ...flow.ActiveProductionVersion.blob.DefaultBlocks || []
    ];

    const executedSequences = await this.findAll({
        where: {
            date: {
                [Op.between]: [start.toDate(), end.toDate()]
            },
            flowId: flowId
        },
        attributes: [
            'sourceBlockId',
            'destBlockId',
            [sequelize.fn('sum', sequelize.col('total')), 'executions']
        ],
        group: ['source_block_id', 'dest_block_id'],
        tableHint: TableHints.NOLOCK
    });

    const blockExecutions = new Map();

    executedSequences.forEach(sequence => {
        if (sequence.sourceBlockId) {
            const currentCount = blockExecutions.get(sequence.sourceBlockId) || 0;
            blockExecutions.set(sequence.sourceBlockId, currentCount + Number(sequence.get('executions')));
        }
        if (sequence.destBlockId) {
            const currentCount = blockExecutions.get(sequence.destBlockId) || 0;
            blockExecutions.set(sequence.destBlockId, currentCount + Number(sequence.get('executions')));
        }
    });

    const executedBlocks = [];
    const unusedBlocks = [];

    allBlocks.forEach(block => {
        const executions = blockExecutions.get(block.Id) || 0;
        if (executions > 0) {
            executedBlocks.push({
                block,
                executions
            });
        } else {
            unusedBlocks.push(block);
        }
    });

    return {
        executedBlocks,
        unusedBlocks
    };
}




const setNameToBlock = async function (flowId, blocks) {
    if (blocks.length === 0) {
        return blocks;
    }
    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }
        let version = flow.ActiveProductionVersion.blob;

        var result = [];
        blocks.forEach((val) => {
            // Obtener solo las propiedades esenciales utilizando la función de mapeo.
            let sourceBlock = DailyByBlocks.setNameToBlock(version, val.sourceBlockId);
            if (!sourceBlock) {
                sourceBlock = version.DefaultBlocks.find(b => b.Id === val.sourceBlockId);
            }

            let destBlock = DailyByBlocks.setNameToBlock(version, val.destBlockId);
            if (!destBlock) {
                destBlock = version.DefaultBlocks.find(b => b.Id === val.destBlockId);
            }

            if (destBlock && sourceBlock) {
                // Acortar las propiedades que se envían, manteniendo solo lo esencial para el reporte.
                val.sourceBlock = {
                    Id: sourceBlock.Id,
                    Name: sourceBlock.Name,
                    Deleted: sourceBlock.Deleted,
                    SystemProtected: sourceBlock.SystemProtected
                };
                val.destBlock = {
                    Id: destBlock.Id,
                    Name: destBlock.Name,
                    Deleted: destBlock.Deleted,
                    SystemProtected: destBlock.SystemProtected
                };
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToBlock, ${err}`);
        throw err;
    });
}


dailyByBlocksSequence.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    total: Sequelize.INTEGER,
    version: Sequelize.INTEGER,
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    sourceBlockId: {
        type: Sequelize.STRING,
        field: 'source_block_id',
    },
    destBlockId: {
        type: Sequelize.STRING,
        field: 'dest_block_id',
    },
    type: {
        type: Sequelize.INTEGER,
        field: 'type',
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_blocks_sequence',
    tableName: 'history_daily_by_blocks_sequence',
    timestamps: false
})

dailyByBlocksSequence.belongsTo(Flow);
module.exports = dailyByBlocksSequence;
