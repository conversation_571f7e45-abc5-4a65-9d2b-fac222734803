const base = require('./dailyBase');
const { Sequelize, Op, TableHints } = require('sequelize');
const sequelize = require('../../helpers/sequelize');
const reduceAll = require('../../helpers/reduceAll');
const Flow = require('../flow');
const moment = require('moment');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const { localeServices } = require('../../i18n.config');
const groupYear = require('../../helpers/Utils').groupYear;
const groupMonth = require('../../helpers/Utils').groupMonth;
const groupDay = require('../../helpers/Utils').groupDay;
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");

class DailyByStatisticEvent extends base.DailyBase {
}

DailyByStatisticEvent.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    total: Sequelize.INTEGER,
    version: Sequelize.INTEGER,
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    statisticEventId: {
        type: Sequelize.INTEGER,
        field: 'statistic_event_id',
    },
    blockId: {
        type: Sequelize.STRING,
        field: 'block_id',
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_statistic_event',
    tableName: 'history_daily_by_statistic_event',
    timestamps: false
})

/**
 * @param {number} flowId flow a filtrar
 * @param {number[]} statisticEventsId integraciobes a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @param {number} offset offset
 * @returns {Promise<DailyByStatisticEvent[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByStatisticEvent.getGroupByDay = async function (flowId, statisticEventsId, start, end, offset) {
    var where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };

    if (statisticEventsId !== undefined &&
        statisticEventsId !== null &&
        statisticEventsId.length != 0) {

        where.statistic_event_id = {
            [Op.in]: statisticEventsId
        };
    }

    const group = [
        groupYear(offset),
        groupMonth(offset),
        groupDay(offset),
        ['statistic_event_id']
    ]

    const order = [[sequelize.literal('date')], ['statistic_event_id', 'asc']];
    const atrributes = [
        [sequelize.fn('CONCAT', groupYear(offset), '-', groupMonth(offset), '-', groupDay(offset)), 'date'],
        'statisticEventId',
        [sequelize.fn('sum', sequelize.col('total')), 'total']
    ];

    return DailyByStatisticEvent.findAll({
        attributes: atrributes,
        where: where,
        group: group,
        order: order,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToStatisticEvent(flowId, result).then((integrations) => {
            return integrations;
        }).catch((err) => {
            console.error(`error en setNameToStatisticEvent, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByDay, ${err}`);
        throw err;
    });
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByStatisticEvent[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByStatisticEvent.getGroupByEvent = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };
    const group = ['flow_id', 'statistic_event_id'
        //, 'block_id'
    ];
    const attributes = [
        //'blockId',
        'statisticEventId',
        [sequelize.fn('sum',
            sequelize.col('total'),
        ), 'total']
    ];

    return DailyByStatisticEvent.findAll({
        attributes: attributes,
        where: where,
        group: group,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToStatisticEvent(flowId, result).then((events) => {
            return events;
        }).catch((err) => {
            console.error(`error en setNameToStatisticEvent, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByDay, ${err}`);
        throw err;
    });
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByStatisticEvent[]>} Devuelve una lista del tipo DailyByFlow
 */
DailyByStatisticEvent.getAllByFlow = async function (flowId, start, end, events = []) {
    var where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId
    };

    if (events !== null && events.length > 0) {
        where.statisticEventId = {
            [Op.in]: events.map(i => Number(i))
        }
    }

    var daily = await DailyByStatisticEvent.reduceAll({}, {
        where: where,
        include: [{
            model: Flow,
            attributes: ['id', 'name']
        }],
        order: [['id', 'DESC']]
    });

    if (daily.length === 0) {
        return daily;
    }

    let flow = await Flow.findLastProductionById(flowId)
    if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
        flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
    }
    let version = flow.ActiveProductionVersion;

    daily.forEach((val) => {
        var block = version.blob.BlockList.find(aux => aux.Id === val.blockId);

        if (!block) {
            block = version.blob.DefaultBlocks.find(aux => aux.Id === val.blockId);
        }

        if (!block) {
            if (version.blob.BlockDeletedList) {
                block = version.blob.BlockDeletedList.find(aux => aux.Id === val.blockId);
                if (block) {
                    block.Deleted = true;
                }
            }
        }

        val.block = block;
        val.statisticEvent = version.blob.StatisticEventList.find(aux => aux.Id === val.statisticEventId);
    });

    return daily;
}

DailyByStatisticEvent.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje, events = []) {
    localeServices.setLocale(lang);
    let daily = await DailyByStatisticEvent.getAllByFlow(flowId, start, end, events);

    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("VERSION")
            , localeServices.translate("EVENT")
            , localeServices.translate("ENABLED")
            , localeServices.translate("BLOCK")
            , localeServices.translate("DELETE")
            , localeServices.translate("EXECUTIONS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        daily.forEach((item) => {
            if (item.block && item.statisticEvent) {
                let date = moment(item.date).utc().add(offset, 'minute');
                let array = [
                    date.format(),
                    intervalTime(date).toString(),
                    item.flow.name,
                    item.channel,
                    (item.version === null ? "" : item.version.toString()),
                    item.statisticEvent.Name,
                    (item.statisticEvent.Enabled !== undefined ? item.statisticEvent.Enabled.toString() : 'false'),
                    item.block.Name,
                    (item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false'),
                    item.total
                ];

                totalWrites++;
                stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
            }
        });

        if (totalWrites === totalCallbacks) {
            console.info(`Se terminó de escribir el archivo ${filename}`);

            stream.end();

            resolve(filename);

            return;
        }

        console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);
        let totalWaits = 0;

        let interval = setInterval(function () {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);

                stream.end();

                clearInterval(interval);

                resolve(filename);
            }
            else {
                totalWaits++;
                if (totalWaits === 5) {
                    console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);

                    stream.end();

                    clearInterval(interval);

                    resolve(filename);
                }
                else {
                    console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);
                }
            }
        }, 2000);
    });
}

DailyByStatisticEvent.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje, events = []) {
    let daily = await DailyByStatisticEvent.getAllByFlow(flowId, start, end, events);
    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    ws.cell(1, 1).string(localeServices.translate("DATE"));
    ws.cell(1, 2).string(localeServices.translate("INTERVAL"));
    ws.cell(1, 3).string(localeServices.translate("FLOW"));
    ws.cell(1, 4).string(localeServices.translate("CHANNEL"));
    ws.cell(1, 5).string(localeServices.translate("VERSION"));
    ws.cell(1, 6).string(localeServices.translate("EVENT"));
    ws.cell(1, 7).string(localeServices.translate("ENABLED"));
    ws.cell(1, 8).string(localeServices.translate("BLOCK"));
    ws.cell(1, 9).string(localeServices.translate("DELETE"));
    ws.cell(1, 10).string(localeServices.translate("EXECUTIONS"));

    var index = 2;
    daily.forEach((item) => {
        if (item.block && item.statisticEvent) {
            var date = moment(item.date).utc().add(offset, 'minute');
            ws.cell(index, 1).date(date);
            ws.cell(index, 2).string(intervalTime(date).toString());
            ws.cell(index, 3).string(item.flow.name);
            ws.cell(index, 4).string(item.channel);
            ws.cell(index, 5).string(item.version === null ? "" : item.version.toString());
            ws.cell(index, 6).string(item.statisticEvent.Name);
            ws.cell(index, 7).string(item.statisticEvent.Enabled !== undefined ? item.statisticEvent.Enabled.toString() : 'false');
            ws.cell(index, 8).string(item.block.Name);
            ws.cell(index, 9).string(item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false');
            ws.cell(index, 10).number(item.total);
            index++;
        }
    });
    return wb;
}

const setNameToStatisticEvent = async function (flowId, events, includeBlocks = false) {
    if (events.length === 0) {
        return events;
    }
    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }
        let version = flow.ActiveProductionVersion;

        var result = Array();
        events.forEach((val) => {
            var block = version.blob.BlockList.find(aux => aux.Id === val.blockId);

            if (!block) {
                block = version.blob.DefaultBlocks.find(aux => aux.Id === val.blockId);
            }

            if (!block) {
                if (version.blob.BlockDeletedList) {
                    block = version.blob.BlockDeletedList.find(aux => aux.Id === val.blockId);
                    if (block) {
                        block.SystemProtected = false;
                        block.Deleted = true;
                    }
                }
            }

            const event = version.blob.StatisticEventList.find(aux => aux.Id === val.statisticEventId);

            if (event) {

                if (block && includeBlocks) {
                    if (block.Deleted === undefined) {
                        block.Deleted = false;
                    }

                    block.Pieces = null;
                    val.block = block;
                }

                val.statisticEvent = event;
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToStatisticEvent, ${err}`);
        throw err;
    });
}
DailyByStatisticEvent.reduceAll = reduceAll;
DailyByStatisticEvent.belongsTo(Flow);
module.exports = DailyByStatisticEvent;
