@import '_variables';
@import "_mixins";

.function-modal-container {
  display: flex;
  flex-direction: column;
  width: 700px;
  min-height: 300px;
  max-height: 800px;
  background: $popup-background-color;
  border-radius: 5px;
  overflow: hidden;
  align-self: center;
  justify-self: center;
  padding-left: 15px;
  padding-right: 15px;

  .contents {
    flex-grow: 1;
    flex-shrink: 1;
    overflow-y: hidden;
    flex-direction: column;
    padding-left: 20px;
    padding-right: 20px;

    .selector {
      margin-bottom: 5px;
      width: 100%;
      .select {
        width: 100%
      }
      .field-label {
          margin: 0;
          font-weight: 1;
      }
    }

    .description {
        padding: 10px 0;
        border-top: 1px solid #000000;
        border-bottom: 1px solid #000000;
    }

    .parameters {
      width: 100%;

      .parameter {
        display: grid;
        align-items: center;
        grid-template-columns: 30% 30% auto;
        padding-top: 5px;

        .parameter-name {
            font-family: $fontFamilyMono;
            .parameter-type {
              font-family: $fontFamilyMono;
              background-color: $flowColor;
              padding: 2px 4px;
              color: #fff;
              margin-left: 3px;
              font-size: 80%;
            }
        }

        .input {
            width: 100%;
        }

        .parameter-description {
          .parameter-required {
            margin-left: 3px;
          }
        }
      }


    }
  }

  .title {
    width: 100%;
    padding: 0 20px;
    margin-bottom: 10px;
    margin-top: 10px;
    line-height: 36px;
    font-size: 20px;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .button-area {
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-content: flex-end;
    margin-top: 5px;
    margin-bottom: 5px;

    .action-button {
      @include popupButton();
    }
  }
}
