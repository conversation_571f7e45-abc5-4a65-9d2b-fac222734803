<div class="decryptPiece card" [ngClass]="{'invalid-piece': !isValid()}">

  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>

  <div class="card-title">
    <span class="fa fa-unlock-alt"></span> {{ 'CARD_DECRYPT_TITLE' | translate }}
  </div>

  <div class="definition">
    <div class="option">
      <span class="title">{{ 'ENCRYPT_TYPE' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptType" [disabled]="readOnly">
        <option *ngFor="let type of encryptTypes" [ngValue]="type.value">
          {{ type.label | translate }}
        </option>
      </select>
    </div>

    <div class="option">
      <span class="title">{{ 'DECRYPTION_KEY' | translate }}:</span>
      <app-input-with-variables class="input" [(value)]="model.DecryptKey" [disabled]="readOnly"
        [validator]="isDecryptKeyValid.bind(this)">
      </app-input-with-variables>
    </div>

    <div class="option" *ngIf="model.DecryptType === EncryptType.AES || model.DecryptType === EncryptType.TripleDES">
      <span class="title">{{ 'DECRYPT_MODE' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptModeType" [disabled]="readOnly">
        <option *ngFor="let mode of decryptModeTypes" [ngValue]="mode.value">
          {{ mode.label | translate }}
        </option>
      </select>
    </div>

    <div class="option" *ngIf="model.DecryptType === EncryptType.AES || model.DecryptType === EncryptType.TripleDES">
      <span class="title">{{ 'DECRYPT_PADDING' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptPaddingType" [disabled]="readOnly">
        <option *ngFor="let padding of decryptPaddingTypes" [ngValue]="padding.value">
          {{ padding.label | translate }}
        </option>
      </select>
    </div>

    <div class="option">
      <span class="title">{{ 'VARIABLE_TO_DECRYPT' | translate }}:</span>
      <app-variable-selector-input class="input"
        [VariableData]="editorService.findVariableWithId(model.VariableToDecryptId)"
        (setVariable)="onSelectVariableToDecrypt($event)" [readOnly]="readOnly" [typeFilters]="[variableTypes.Text]"
        [validator]="isVariableToDecryptValid.bind(this)">
      </app-variable-selector-input>
    </div>

    <div class="option">
      <span class="title">{{ 'VARIABLE_DECRYPTED' | translate }}:</span>
      <app-variable-selector-input class="input"
        [VariableData]="editorService.findVariableWithId(model.VariableDecryptedId)"
        (setVariable)="onSelectVariableDecrypted($event)" [readOnly]="readOnly"
        [typeFilters]="[variableTypes.Text, variableTypes.Number, variableTypes.Decimal, variableTypes.Bool, variableTypes.Array]"
        [validator]="isVariableDecryptedValid.bind(this)">
      </app-variable-selector-input>
    </div>
    <!-- NO TIENE USO, SE DEJA COMENTADO
      <div class="option" >
        <span class="title">{{ 'ENCRYPT_ERROR_MESSAGES' | translate }}:</span>
        <app-input-with-variables [(value)]="model.ErrorMessage" [isTextArea]="true" [wideInput]="true"
          [disabled]="readOnly" [placeholder]="'ENCRYPT_ERROR_MESSAGES' | translate">
        </app-input-with-variables>
      </div>
    -->

    <div class="option">
      <span class="title">{{ 'ON_ERROR_GO_TO' | translate }}:</span>
      <app-block-picker class="input" [blockId]="model.ErrorBlockId" (onSelectNewBlock)="onSelectErrorBlock($event)"
        (onDeleteBlock)="onDeleteErrorBlock()" [readOnly]="readOnly" [isInvalid]="!isErrorBlockValid()">
      </app-block-picker>
    </div>
  </div>
</div>
