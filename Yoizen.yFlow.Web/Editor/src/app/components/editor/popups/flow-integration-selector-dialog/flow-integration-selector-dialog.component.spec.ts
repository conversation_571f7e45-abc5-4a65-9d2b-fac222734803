import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FlowIntegrationSelectorDialogComponent } from './flow-integration-selector-dialog.component';

describe('FlowIntegrationSelectorDialogComponent', () => {
  let component: FlowIntegrationSelectorDialogComponent;
  let fixture: ComponentFixture<FlowIntegrationSelectorDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FlowIntegrationSelectorDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FlowIntegrationSelectorDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
