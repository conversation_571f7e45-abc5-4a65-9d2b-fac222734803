<div class="flow-selector-container" *ngIf="SelectedFlow === null">
  <div class="title">{{ 'FLOW_SELECTOR_TITLE' | translate }}</div>
  <div class="contents">
    <h2>{{ 'FLOWS_PUBLISHED' | translate }}</h2>
    <!--TODO: ver como meter esto de nuevo
    <app-dashboard-flow *ngFor="let flow of publishedFlows" class="content"
                        [model]="flow"
                        [isStaging]="false"
                        [showActions]="false"
                        [onSelect]="onFlowSelected.bind(this)"></app-dashboard-flow>
    <label *ngIf="!publishedFlows || publishedFlows.length == 0">{{'NO_FLOWS' | translate}}</label>-->
    <h2>{{ 'FLOWS_DEVELOPMENT' | translate }}</h2>
    <app-dashboard-flow *ngFor="let flow of stagingFlows" class="content development"
                        [model]="flow"
                        [showActions]="false"
                        [onSelect]="onFlowSelected.bind(this)"></app-dashboard-flow>
    <label *ngIf="!stagingFlows || stagingFlows.length == 0">{{'NO_FLOWS' | translate}}</label>
  </div>
  <div class="button-area">
    <div class="action-button action-button-default" (click)="closePopup()">{{ 'CANCEL' | translate}}</div>
  </div>
</div>

<div class="integration-selector-container" *ngIf="SelectedFlow !== null">
  <div class="title">{{ 'INTEGRATION_SELECTOR_FROM_FLOW_TITLE' | translate:flowTitleParam }}</div>
  <div class="contents">
    <div class="integrations-list">
      <div class="integrations">
        <div class="empty" *ngIf="IntegrationDefinitions !== null && IntegrationDefinitions.length === 0" role="alert">
          <div class="alert alert-info">
            {{ 'INTEGRATIONS_EMPTY' | translate }}
          </div>
        </div>
        <app-integration-button *ngFor="let integration of IntegrationDefinitions"
                                (click)="SelectedIntegration = integration"
                                [name]="integration.name"
                                [isSelected]="SelectedIntegration === integration"
                                [isValid]="integration.isValid()"
                                [showActions]="false"></app-integration-button>
      </div>
    </div>
  </div>
  <div class="button-area">
    <div class="action-button" (click)="goBack()">{{ 'BACK' | translate}}</div>
    <div class="action-button action-button-default" (click)="acceptPopup()" [ngClass]="{'disabled': SelectedBlock === null}">{{ 'ACCEPT' | translate}}</div>
    <div class="action-button" (click)="closePopup()">{{ 'CANCEL' | translate}}</div>
  </div>
</div>
