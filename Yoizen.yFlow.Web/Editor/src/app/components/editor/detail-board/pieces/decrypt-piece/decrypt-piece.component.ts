import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { DecryptPiece } from 'src/app/models/pieces/DecryptPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { EncryptModeType, EncryptPaddingType, EncryptType  } from 'src/app/models/pieces/AccountLinkingPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';

@Component({
  selector: 'app-decrypt-piece',
  templateUrl: './decrypt-piece.component.html',
  styleUrls: ['./decrypt-piece.component.scss']
})
export class DecryptPieceComponent extends BasePieceVM implements OnInit {
  model: DecryptPiece;

  encryptTypes = [
    { value: EncryptType.AES, label: 'ENCRYPT_TYPE_AES' },
    { value: EncryptType.TripleDES, label: 'ENCRYPT_TYPE_TRIPLEDES' },
    { value: EncryptType.Rabbit, label: 'ENCRYPT_TYPE_RABBIT' },
  ];

  decryptModeTypes = [
    { value: EncryptModeType.CBC, label: 'ENCRYPT_MODE_CBC' },
    { value: EncryptModeType.CFB, label: 'ENCRYPT_MODE_CFB' },
    { value: EncryptModeType.CTR, label: 'ENCRYPT_MODE_CTR' },
    { value: EncryptModeType.OFB, label: 'ENCRYPT_MODE_OFB' },
    { value: EncryptModeType.ECB, label: 'ENCRYPT_MODE_ECB' },
  ];

  decryptPaddingTypes = [
    { value: EncryptPaddingType.Pkcs7, label: 'ENCRYPT_PADDING_PKCS7' },
    { value: EncryptPaddingType.Iso97971, label: 'ENCRYPT_PADDING_ISO97971' },
    { value: EncryptPaddingType.AnsiX923, label: 'ENCRYPT_PADDING_ANSIX923' },
    { value: EncryptPaddingType.Iso10126, label: 'ENCRYPT_PADDING_ISO10126' },
    { value: EncryptPaddingType.ZeroPadding, label: 'ENCRYPT_PADDING_ZEROPADDING' },
    { value: EncryptPaddingType.NoPadding, label: 'ENCRYPT_PADDING_NOPADDING' },
  ];

  variableTypes = TypeDefinition;
  EncryptType = EncryptType;

  constructor(public editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as DecryptPiece;
    if (!this.model.DecryptType) {
      this.model.DecryptType = EncryptType.AES; 
    }
  }

  onSelectVariableToDecrypt(variableData: VariableDefinition) {
    this.model.VariableToDecryptId = variableData ? variableData.Id : null;
  }

  onSelectVariableDecrypted(variableData: VariableDefinition) {
    this.model.VariableDecryptedId = variableData ? variableData.Id : null;
  }

  onSelectErrorBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock() {
    this.model.ErrorBlockId = "-1";
  }

  isValid(): boolean {
    return this.isDecryptKeyValid() &&
           this.isVariableToDecryptValid() &&
           this.isVariableDecryptedValid() &&
           this.isErrorBlockValid();
  }

  isDecryptKeyValid(): boolean {
    return this.model.DecryptKey && this.model.DecryptKey.trim().length > 0;
  }

  isVariableToDecryptValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableToDecryptId);
    return variable != null && variable.Type === this.variableTypes.Text;
  }

  isVariableDecryptedValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableDecryptedId);
    return variable != null && (
      variable.Type === this.variableTypes.Text ||
      variable.Type === this.variableTypes.Number ||
      variable.Type === this.variableTypes.Decimal ||
      variable.Type === this.variableTypes.Bool ||
      variable.Type === this.variableTypes.Array
    );
  }

  isErrorBlockValid(): boolean {
    const block = this.editorService.findBlockWithId(this.model.ErrorBlockId);
    return this.model.ErrorBlockId !== "-1" && block != null && block.validateCurrentModuleBlock(this.editorService);
  }
}