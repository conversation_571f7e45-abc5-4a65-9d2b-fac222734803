import { EditorService } from './../../../../services/editor.service';
import { Concatenate } from './../../../../models/pieces/Concatenate';
import { VariableDefinition } from './../../../../models/VariableDefinition';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { InternalFunction } from './../../../../models/InternalFunction';
import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import FunctionsJson from "../../../../../assets/context_functions.json";
import { ToasterService } from 'angular2-toaster';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-function-modal',
  templateUrl: './function-modal.component.html',
  styleUrls: ['./function-modal.component.scss']
})
export class FunctionModalComponent implements OnInit {
  functions: InternalFunction[];
  functionTypes: string;
  currentFunctionType: string;
  currentFunction: InternalFunction;
  parameterValues: string[];
  @Output() ReturnFunctionAction: EventEmitter<any> = null;

  constructor(private modalService: ModalService, private editorService: EditorService, private toasterService: ToasterService, private translateService: TranslateService) { }
  
  ngOnInit() {
    this.functions = FunctionsJson.functions;
    this.functionTypes = FunctionsJson.available_types;
    if (this.functionTypes.length > 0) {
      this.currentFunctionType = this.functionTypes[0];
      let filteredFunctions = this.getFunctionsByType(this.functionTypes[0]);
      if (filteredFunctions.length > 0) {
        this.currentFunction = filteredFunctions[0];
        this.updateParameters();
      } else {
        console.log("Se encontraron los tipos de funciones, pero no estan asociados a ninguna función existente");
      }
    } else {
      console.log("No se encontraron los tipos de funciones cargados en el json");
    }
  }

  get customVariables(): VariableDefinition[] {
    return Concatenate.SpecialVar;
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  private getFunctionsByType(type: string) {
    return this.functions.filter(f => f.Type === type);
  }

  updateType() {
    let filteredFuncs = this.getFunctionsByType(this.currentFunctionType);
    if (filteredFuncs.length > 0) {
      this.currentFunction = filteredFuncs[0];
    } else {
      console.log("El tipo seleccionado no tiene funciones cargadas");
    }
    this.updateParameters();
  }

  updateParameters() {
    this.parameterValues = [];
    this.currentFunction.Parameters.forEach(p => {
      this.parameterValues.push("");
    });
    console.log(this.parameterValues);
  }

  loadFunction() {
    for (let i = 0; i < this.parameterValues.length; i++) {
      if (!this.parameterValues[i] && this.currentFunction.Parameters[i].IsRequired) {
        this.toasterService.pop('warning', this.translateService.instant('FUNCTION_PARAMETERS_IS_REQUIRED'));
        return;
      }
    }
    let params = "";
    this.currentFunction.Parameters.forEach((param, i) => {
      let aux = "";
      if (param.Type === "string" || param.Type === "date") {
        if (this.parameterValues[i].indexOf('}}') === -1 &&
            this.parameterValues[i].indexOf('}$') === -1) {
              aux = `"${this.parameterValues[i]}"`;
            }
            else{
              aux = this.parameterValues[i].replace(/{{|}}|\${|}\$/g, "");
            }
      } else {
        aux = this.parameterValues[i].replace(/{{|}}|\${|}\$/g, "");
      }
      params += aux;
      
      if (i !== this.currentFunction.Parameters.length-1) {
        params += ',';
      }
    });
    this.ReturnFunctionAction.emit("$"+`{${this.currentFunction.Name}(${params})}`+"$");
    this.modalService.destroy();
  }

  onCancel() {
    this.modalService.destroy();
  }

}
