import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EncryptPiece } from 'src/app/models/pieces/EncryptPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { EncryptModeType, EncryptPaddingType, EncryptType  } from 'src/app/models/pieces/AccountLinkingPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';

@Component({
  selector: 'app-encrypt-piece',
  templateUrl: './encrypt-piece.component.html',
  styleUrls: ['./encrypt-piece.component.scss']
})
export class EncryptPieceComponent extends BasePieceVM implements OnInit {
  model: EncryptPiece;

  encryptTypes = [
    { value: EncryptType.AES, label: 'ENCRYPT_TYPE_AES' },
    { value: EncryptType.TripleDES, label: 'ENCRYPT_TYPE_TRIPLEDES' },
    { value: EncryptType.Rabbit, label: 'ENCRYPT_TYPE_RABBIT' },
  ];

  encryptModeTypes = [
    { value: EncryptModeType.CBC, label: 'ENCRYPT_MODE_CBC' },
    { value: EncryptModeType.CFB, label: 'ENCRYPT_MODE_CFB' },
    { value: EncryptModeType.CTR, label: 'ENCRYPT_MODE_CTR' },
    { value: EncryptModeType.OFB, label: 'ENCRYPT_MODE_OFB' },
    { value: EncryptModeType.ECB, label: 'ENCRYPT_MODE_ECB' },
  ];

  encryptPaddingTypes = [
    { value: EncryptPaddingType.Pkcs7, label: 'ENCRYPT_PADDING_PKCS7' },
    { value: EncryptPaddingType.Iso97971, label: 'ENCRYPT_PADDING_ISO97971' },
    { value: EncryptPaddingType.AnsiX923, label: 'ENCRYPT_PADDING_ANSIX923' },
    { value: EncryptPaddingType.Iso10126, label: 'ENCRYPT_PADDING_ISO10126' },
    { value: EncryptPaddingType.ZeroPadding, label: 'ENCRYPT_PADDING_ZEROPADDING' },
    { value: EncryptPaddingType.NoPadding, label: 'ENCRYPT_PADDING_NOPADDING' },
  ];
  outputVariableFilter = [TypeDefinition.Text,TypeDefinition.Object];

  variableTypes = TypeDefinition;
  EncryptType = EncryptType;

  constructor(public editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as EncryptPiece;
    if (!this.model.EncryptType) {
      this.model.EncryptType = EncryptType.AES; 
    }
  }

  onSelectVariableToEncrypt(variableData: VariableDefinition) {
    this.model.VariableToEncryptId = variableData ? variableData.Id : null;
  }

  onSelectVariableEncrypted(variableData: VariableDefinition) {
    this.model.VariableEncryptedId = variableData ? variableData.Id : null;
  }

  onSelectErrorBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock() {
    this.model.ErrorBlockId = "-1";
  }

  isValid(): boolean {
    return this.isEncryptKeyValid() &&
           this.isVariableToEncryptValid() &&
           this.isVariableEncryptedValid() &&
           this.isErrorBlockValid();
  }

  isEncryptKeyValid(): boolean {
    return this.model.EncryptKey && this.model.EncryptKey.trim().length > 0;
  }

  isVariableToEncryptValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableToEncryptId);
    return variable != null && this.outputVariableFilter.includes(variable.Type);
  }
  
  isVariableEncryptedValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableEncryptedId);
    return variable != null && (
      variable.Type === this.variableTypes.Text ||
      variable.Type === this.variableTypes.Number ||
      variable.Type === this.variableTypes.Decimal ||
      variable.Type === this.variableTypes.Bool ||
      variable.Type === this.variableTypes.Array
    );
  }

  isErrorBlockValid(): boolean {
    const block = this.editorService.findBlockWithId(this.model.ErrorBlockId);
    return this.model.ErrorBlockId !== "-1" && block != null && block.validateCurrentModuleBlock(this.editorService);
  }
}