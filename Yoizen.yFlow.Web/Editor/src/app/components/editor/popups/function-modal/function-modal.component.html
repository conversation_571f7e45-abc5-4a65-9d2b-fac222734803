<div class="function-modal-container">
  <div class="title">{{ 'HELP_FUNCTIONS' | translate }}</div>
  <div class="contents">
    <!--<div class="alert alert-danger" *ngIf="error">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{error}}
    </div>-->
    <div class="selector">
      <label class="field-label">{{'FUNCTION_TYPE' | translate}}</label>
      <select class="select" [(ngModel)]="currentFunctionType" (change)="updateType()">
        <option *ngFor="let type of functionTypes" [ngValue]="type">
          {{type}}
        </option>
      </select>
    </div>
    <div class="selector">
      <label class="field-label">{{'FUNCTION_NAME' | translate}}</label>
      <select class="select" [(ngModel)]="currentFunction" (change)="updateParameters()">
        <option *ngFor="let func of functions | functionFilterPipe:currentFunctionType" [ngValue]="func">
          {{func.Name}}
        </option>
      </select>
    </div>
    <div class="description">
      <span>{{currentFunction.Description | translate}}</span>
      <footer class="blockquote-footer">{{'FUNCTION_TABLE_QUOTE' | translate}}</footer>
    </div>
    <div class="parameters">
      <div class="parameter" *ngFor="let param of currentFunction.Parameters; index as i;">
        <span class="parameter-name">
          {{param.Name | translate}}<span class="parameter-type">{{ param.Type | translate }}</span>
        </span>
        <app-input-with-variables
          [placeholder]="''"
          [(value)]="parameterValues[i]"
          [spellCheck]="false"
          [customVariableList]="customVariables"
          [variableFinder]="searchForVariable.bind(this)"
          [JoinCustomVariable]="true"
          [insideHelp]="true"
        >
        </app-input-with-variables>
        <!--<input class="input" [(ngModel)]="parameterValues[i]">-->
        <span class="parameter-description">{{param.Description | translate}}<span class="parameter-required" *ngIf="param.IsRequired">(*)</span></span>
      </div>
    </div>
  </div>
  <div class="button-area">
    <button type="button" class="action-button action-button-default" (click)="loadFunction()">{{'INSERT' | translate}}</button>
    <button type="button" class="action-button" (click)="onCancel()">{{'CANCEL' | translate}}</button>
  </div>
</div>
