const base = require('./dailyBase');
const { Sequelize, Op, TableHints } = require('sequelize');
const sequelize = require('../../helpers/sequelize');
const Flow = require('../flow');
const moment = require('moment');
const xls = require('excel4node');
const { intervalTime } = require('./interval');
const DailyByGroups = require('./dailyByGroups');
const { localeServices } = require('../../i18n.config');
const { stringify } = require('csv-stringify/sync');
const { reportsFolder } = require("../../helpers/folders");
const fs = require("fs");

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}
class dailyByGroupsSequence extends base.DailyBase {
}

/**
 * @param {number} flowId flow a filtrar
 * @param {moment.Moment} start fecha de inicio
 * @param {moment.Moment} end fecha de fin
 * @returns {Promise<DailyByBlocks[]>} Devuelve una lista del tipo DailyByFlow
 */
dailyByGroupsSequence.getGroupByBlockSequence = async function (flowId, groupId, start, end) {

    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flow_id: flowId,
        /*[Op.or]: [
            {source_group_id : groupId},
            {dest_group_id : groupId}
        ]*/
    };
    const group = ['flow_id', 'dest_group_id', 'source_group_id'];
    const attributes = [
        'destGroupId',
        'sourceGroupId',
        [sequelize.fn('sum',
            sequelize.col('total'),
        ), 'total'],
    ];

    return dailyByGroupsSequence.findAll({
        attributes: attributes,
        where: where,
        group: group,
        tableHint: TableHints.NOLOCK
    }).then((result) => {
        return setNameToGroupsSequence(flowId, result).then((groups) => {
            return groups;
        }).catch((err) => {
            console.error(`error en setNameToGroups, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en getGroupByGroupSequence, ${err}`);
        throw err;
    });
}

dailyByGroupsSequence.generateExcel = async function (flowId, start, end, offset, lang = process.env.defaultLanguaje) {
    let blocks = await dailyByGroupsSequence.getAllByFlow(flowId, start, end);

    const wb = new xls.Workbook();
    const ws = wb.addWorksheet("Hoja1");

    wb.createStyle({
        border: {
            bottom: {
                style: 'double',
                color: '000000'
            }
        },
    });

    localeServices.setLocale(lang);

    ws.cell(1, 1).string(localeServices.translate("DATE"));
    ws.cell(1, 2).string(localeServices.translate("INTERVAL"));
    ws.cell(1, 3).string(localeServices.translate("FLOW"));
    ws.cell(1, 4).string(localeServices.translate("CHANNEL"));
    ws.cell(1, 5).string(localeServices.translate("VERSION"));
    ws.cell(1, 6).string(localeServices.translate("SOURCE_GROUP"));
    ws.cell(1, 7).string(localeServices.translate("DEST_GROUP"));
    ws.cell(1, 8).string(localeServices.translate("EXECUTIONS"));

    var index = 2;
    blocks.forEach((item) => {
        if (item.sourceGroup && item.destGroup) {
            var date = moment(item.date).utc().add(offset, 'minute');
            ws.cell(index, 1).date(date);
            ws.cell(index, 2).string(intervalTime(date).toString());
            ws.cell(index, 3).string(item.flow.name);
            ws.cell(index, 4).string(item.channel);
            ws.cell(index, 5).string(item.version.toString());
            ws.cell(index, 6).string(item.sourceGroup.Name);
            ws.cell(index, 7).string(item.destGroup.Name);
            //ws.cell(index, 7).string(item.block.Deleted !== undefined ? item.block.Deleted.toString() : 'false');
            //ws.cell(index, 8).string(item.block.SystemProtected !== undefined ? item.block.SystemProtected.toString() : 'false');
            ws.cell(index, 8).number(item.total);
            index++;
        }
    });

    return wb;
}

dailyByGroupsSequence.getAllByFlow = async function (flowId, start, end) {
    const where = {
        date: {
            [Op.between]: [start.toDate(), end.toDate()]
        },
        flowId: flowId
    };

    return dailyByGroupsSequence.reduceAll({}, {
        where: where,
        include: [{
            model: Flow,
            attributes: ['id', 'name']
        }],
    }).then((result) => {
        return setNameToGroupsSequence(flowId, result).then((blocks) => {
            return blocks;
        }).catch((err) => {
            console.error(`error en setNameToBlocks, ${err}`);
            throw err;
        });
    }).catch((err) => {
        console.error(`error en setNameToBlocks, ${err}`);
        throw err;
    });
}


dailyByGroupsSequence.generateText = async function (reportId, flowId, start, end, offset, operator, lang = process.env.defaultLanguaje) {
    localeServices.setLocale(lang);
    let blocks = await dailyByGroupsSequence.getAllByFlow(flowId, start, end);

    let filename = `${reportsFolder}/${reportId}.csv`;
    console.info(`Se generará el reporte ${filename}`);

    return new Promise(resolve => {
        let stream = fs.createWriteStream(filename);

        let totalWrites = 0;
        let totalCallbacks = 0;

        let titles = [
            localeServices.translate("DATE")
            , localeServices.translate("INTERVAL")
            , localeServices.translate("FLOW")
            , localeServices.translate("CHANNEL")
            , localeServices.translate("VERSION")
            , localeServices.translate("SOURCE_GROUP")
            , localeServices.translate("DEST_GROUP")
            , localeServices.translate("EXECUTIONS")
        ];

        totalWrites++;
        stream.write(stringify([titles], { delimiter: operator }), () => { totalCallbacks++ });

        blocks.forEach((item) => {
            if (item.sourceGroup && item.destGroup) {
                let date = moment(item.date).utc().add(offset, 'minute');
                let array = [
                    date.format(),
                    intervalTime(date).toString(),
                    item.flow.name,
                    item.channel,
                    item.version.toString(),
                    item.sourceGroup.Name,
                    item.destGroup.Name,
                    item.total
                ];

                totalWrites++;
                stream.write(stringify([array], { delimiter: operator }), () => { totalCallbacks++ });
            }
        });

        if (totalWrites === totalCallbacks) {
            console.info(`Se terminó de escribir el archivo ${filename}`);

            stream.end();

            resolve(filename);

            return;
        }

        console.info(`Se aguardará unos segundos para dejar terminar escribir el archivo ${filename}`);
        let totalWaits = 0;

        let interval = setInterval(function () {
            if (totalWrites === totalCallbacks) {
                console.info(`Se terminó de escribir el archivo ${filename}`);

                stream.end();

                clearInterval(interval);

                resolve(filename);
            }
            else {
                totalWaits++;
                if (totalWaits === 5) {
                    console.info(`Ya se esperó ${totalWaits} y se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. No se espera más`);

                    stream.end();

                    clearInterval(interval);

                    resolve(filename);
                }
                else {
                    console.info(`Se escribieron ${totalWrites} pero se recibieron ${totalCallbacks}. Esperas: ${totalWaits}. Se sigue esperando`);
                }
            }
        }, 2000);
    });
}

const setNameToGroupsSequence = async function (flowId, blocks) {
    if (blocks.length === 0) {
        return blocks;
    }
    return Flow.findLastProductionById(flowId).then((flow) => {
        if (typeof (flow.ActiveProductionVersion.blob) === 'string') {
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);
        }
        let version = flow.ActiveProductionVersion;

        var result = Array();
        blocks.forEach((val) => {
            const sourceGroup = DailyByGroups.setNameToGroup(version, val.sourceGroupId);
            const destGroup = DailyByGroups.setNameToGroup(version, val.destGroupId);
            if (sourceGroup && destGroup) {
                val.destGroup = destGroup;
                val.sourceGroup = sourceGroup;
                result.push(val);
            }
        });

        return result;
    }).catch((err) => {
        console.error(`error en setNameToGroups, ${err}`);
        throw err;
    });
}

dailyByGroupsSequence.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: Sequelize.DATE,
    datetime: {
        type: Sequelize.DATE,
        field: 'interval_datetime'
    },
    interval: Sequelize.INTEGER,
    total: Sequelize.INTEGER,
    version: Sequelize.INTEGER,
    flowId: {
        type: Sequelize.INTEGER,
        field: 'flow_id',
        references: {
            model: Flow,
            key: 'id'
        }
    },
    sourceGroupId: {
        type: Sequelize.INTEGER,
        field: 'source_group_id',
    },
    destGroupId: {
        type: Sequelize.INTEGER,
        field: 'dest_group_id',
    },
    type: {
        type: Sequelize.INTEGER,
        field: 'type',
    },
    channel: Sequelize.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_groups_sequence',
    tableName: 'history_daily_by_groups_sequence',
    timestamps: false
})

dailyByGroupsSequence.belongsTo(Flow);
module.exports = dailyByGroupsSequence;
