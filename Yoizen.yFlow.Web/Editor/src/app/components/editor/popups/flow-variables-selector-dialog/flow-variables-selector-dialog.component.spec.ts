import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FlowVariablesSelectorDialogComponent } from './flow-variables-selector-dialog.component';

describe('FlowVariablesSelectorDialogComponent', () => {
  let component: FlowVariablesSelectorDialogComponent;
  let fixture: ComponentFixture<FlowVariablesSelectorDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FlowVariablesSelectorDialogComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FlowVariablesSelectorDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
